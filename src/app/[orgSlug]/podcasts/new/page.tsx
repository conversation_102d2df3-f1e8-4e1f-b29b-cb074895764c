"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import {
  ChevronDown,
  ChevronRight,
  Loader2,
  Trash2,
  Upload,
} from "lucide-react";
import type { NextPage } from "next";
import { useState } from "react";
import { type UseFormReturn, useFieldArray, useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  InputGroup,
  InputGroupAddon,
  InputGroupInput,
  InputGroupText,
} from "@/components/ui/input-group";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useTRPC } from "@/server/trpc/client";

const episodeSchema = z.object({
  name: z.string().min(1, "Episode name is required"),
  description: z.string().optional(),
  image: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  audio: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  duration: z.string().optional(),
});

const seasonSchema = z.object({
  name: z.string().min(1, "Season name is required"),
  description: z.string().optional(),
  image: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  episodes: z.array(episodeSchema),
});

const podcastFormSchema = z.object({
  name: z.string().min(1, "Podcast name is required"),
  subdomain: z.string().optional(),
  description: z.string().optional(),
  image: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  email: z.string().email("Must be a valid email").optional().or(z.literal("")),
  owner: z.string().optional(),
  copyright: z.string().optional(),
  seasons: z.array(seasonSchema),
});

type PodcastFormValues = z.infer<typeof podcastFormSchema>;

const PodcastNewPage: NextPage = () => {
  const trpc = useTRPC();

  const [rssUrl, setRssUrl] = useState("");
  const [isImporting, setIsImporting] = useState(false);
  const [openSeasons, setOpenSeasons] = useState<Set<number>>(new Set());

  const { mutateAsync: parseRSS } = useMutation(
    trpc.parse.parseRSS.mutationOptions(),
  );

  const form = useForm<PodcastFormValues>({
    resolver: zodResolver(podcastFormSchema),
    defaultValues: {
      name: "",
      subdomain: "",
      description: "",
      image: "",
      email: "",
      owner: "",
      copyright: "",
      seasons: [],
    },
  });

  const { fields: seasonFields, remove: removeSeason } = useFieldArray({
    control: form.control,
    name: "seasons",
  });

  const handleImportFromRss = async () => {
    if (!rssUrl.trim()) {
      toast.error("Please enter a valid RSS feed URL");
      return;
    }

    setIsImporting(true);
    try {
      const { data } = await parseRSS({ url: rssUrl }, {});

      form.reset({
        name: data.title || "",
        subdomain: data.title?.toLowerCase().replace(/[^a-z0-9]+/g, "-") || "",
        description: data.description || "",
        image: data.image || "",
        email: data.email || "",
        owner: data.owner || "",
        copyright: data.copyright || "",
        seasons: data.seasons || [],
      });

      if (data.seasons && data.seasons.length > 0) {
        setOpenSeasons(new Set([0]));
      }

      toast.success("RSS feed imported successfully");
    } catch (error) {
      console.error(error);
      toast.error("Failed to import RSS feed");
    } finally {
      setIsImporting(false);
    }
  };

  const onSubmit = async (values: PodcastFormValues) => {
    console.log("Submitting podcast:", values);
    toast.info("Submitting podcast...");
  };

  const toggleSeason = (index: number) => {
    setOpenSeasons((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  return (
    <>
      <section className="flex items-end justify-between">
        <div className="space-y-4">
          <div className="space-y-2">
            <h1 className="text-4xl font-semibold tracking-tight">
              Create Podcast
            </h1>
            <p className="text-muted-foreground text-lg">
              Create a new podcast here. Or comfortably import by passing a feed
              URL.
            </p>
          </div>
        </div>
      </section>
      <section className="py-12">
        <div className="space-y-2">
          <Label htmlFor="rss-url">RSS Feed URL</Label>
          <div className="flex gap-2">
            <Input
              id="rss-url"
              type="url"
              placeholder="https://example.com/podcast/feed.xml"
              value={rssUrl}
              onChange={(e) => setRssUrl(e.target.value)}
              className="flex-1"
            />
            <Button
              type="button"
              onClick={handleImportFromRss}
              disabled={isImporting}
            >
              {isImporting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Importing
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Import
                </>
              )}
            </Button>
          </div>
          <p className="text-muted-foreground text-sm">
            Enter your podcast RSS feed URL to automatically populate the form
            fields
          </p>
        </div>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-6 mt-6"
          >
            <div className="grid gap-6 md:grid-cols-3 items-start">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Podcast Name <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="My Awesome Podcast" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="subdomain"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Subdomain</FormLabel>
                    <FormControl>
                      <InputGroup>
                        <InputGroupInput placeholder="my-podcast" {...field} />
                        <InputGroupAddon align="inline-end">
                          <InputGroupText>.puka.cloud</InputGroupText>
                        </InputGroupAddon>
                      </InputGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Tell us about your podcast..."
                      rows={4}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="image"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cover Image URL</FormLabel>
                  <FormControl>
                    <Input
                      type="url"
                      placeholder="https://example.com/cover.jpg"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid gap-6 md:grid-cols-3">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Contact Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="owner"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Owner</FormLabel>
                    <FormControl>
                      <Input placeholder="John Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="copyright"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Copyright</FormLabel>
                    <FormControl>
                      <Input placeholder="© 2025 My Podcast" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {seasonFields.length > 0 && (
              <div className="space-y-4 pt-4 border-t">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold">
                      Seasons & Episodes
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {seasonFields.length} season
                      {seasonFields.length !== 1 ? "s" : ""} with{" "}
                      {seasonFields.reduce(
                        (acc, s) => acc + s.episodes.length,
                        0,
                      )}{" "}
                      episode
                      {seasonFields.reduce(
                        (acc, s) => acc + s.episodes.length,
                        0,
                      ) !== 1
                        ? "s"
                        : ""}
                    </p>
                  </div>
                </div>

                <div className="space-y-3">
                  {seasonFields.map((season, seasonIndex) => (
                    <SeasonFormSection
                      key={season.id}
                      seasonIndex={seasonIndex}
                      form={form}
                      isOpen={openSeasons.has(seasonIndex)}
                      onToggle={() => toggleSeason(seasonIndex)}
                      onRemove={() => removeSeason(seasonIndex)}
                    />
                  ))}
                </div>
              </div>
            )}

            <div className="flex justify-end gap-3 pt-4">
              <Button type="button" variant="outline">
                Cancel
              </Button>
              <Button type="submit">Create Podcast</Button>
            </div>
          </form>
        </Form>
      </section>
    </>
  );
};

export default PodcastNewPage;

function SeasonFormSection({
  seasonIndex,
  form,
  isOpen,
  onToggle,
  onRemove,
}: {
  seasonIndex: number;
  form: UseFormReturn<PodcastFormValues>;
  isOpen: boolean;
  onToggle: () => void;
  onRemove: () => void;
}) {
  const { fields: episodeFields, remove: removeEpisode } = useFieldArray({
    control: form.control,
    name: `seasons.${seasonIndex}.episodes`,
  });

  return (
    <Collapsible open={isOpen} onOpenChange={onToggle}>
      <Card>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-accent/50 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 flex-1">
                {isOpen ? (
                  <ChevronDown className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                ) : (
                  <ChevronRight className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                )}
                <div className="flex-1 min-w-0">
                  <CardTitle className="text-base truncate">
                    {form.watch(`seasons.${seasonIndex}.name`) ||
                      `Season ${seasonIndex + 1}`}
                  </CardTitle>
                  <CardDescription className="text-sm">
                    {episodeFields.length} episode
                    {episodeFields.length !== 1 ? "s" : ""}
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">{episodeFields.length}</Badge>
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={(e) => {
                    e.stopPropagation();
                    onRemove();
                  }}
                  className="h-8 w-8"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <CardContent className="pt-0 space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name={`seasons.${seasonIndex}.name`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Season Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Season 1" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`seasons.${seasonIndex}.image`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Season Image URL</FormLabel>
                    <FormControl>
                      <Input
                        type="url"
                        placeholder="https://example.com/season.jpg"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name={`seasons.${seasonIndex}.description`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Season Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Season description..."
                      rows={2}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-2 pt-2">
              <h4 className="text-sm font-semibold">Episodes</h4>
              {episodeFields.map((episode, episodeIndex) => (
                <EpisodeFormSection
                  key={episode.id}
                  seasonIndex={seasonIndex}
                  episodeIndex={episodeIndex}
                  form={form}
                  onRemove={() => removeEpisode(episodeIndex)}
                />
              ))}
            </div>
          </CardContent>
        </CollapsibleContent>
      </Card>
    </Collapsible>
  );
}

function EpisodeFormSection({
  seasonIndex,
  episodeIndex,
  form,
  onRemove,
}: {
  seasonIndex: number;
  episodeIndex: number;
  form: UseFormReturn<PodcastFormValues>;
  onRemove: () => void;
}) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card className="bg-muted/30">
      <CardHeader className="py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-6 w-6 flex-shrink-0"
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
            <span className="text-sm font-medium truncate">
              {form.watch(
                `seasons.${seasonIndex}.episodes.${episodeIndex}.name`,
              ) || `Episode ${episodeIndex + 1}`}
            </span>
          </div>
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={onRemove}
            className="h-6 w-6 flex-shrink-0"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </CardHeader>
      {isExpanded && (
        <CardContent className="pt-0 pb-3 space-y-3">
          <FormField
            control={form.control}
            name={`seasons.${seasonIndex}.episodes.${episodeIndex}.name`}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-xs">Episode Name</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Episode title"
                    {...field}
                    className="h-8 text-sm"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`seasons.${seasonIndex}.episodes.${episodeIndex}.description`}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-xs">Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Episode description..."
                    rows={2}
                    {...field}
                    className="text-sm"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid gap-3 md:grid-cols-2">
            <FormField
              control={form.control}
              name={`seasons.${seasonIndex}.episodes.${episodeIndex}.image`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-xs">Image URL</FormLabel>
                  <FormControl>
                    <Input
                      type="url"
                      placeholder="https://..."
                      {...field}
                      className="h-8 text-sm"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`seasons.${seasonIndex}.episodes.${episodeIndex}.duration`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-xs">Duration</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="45:30"
                      {...field}
                      className="h-8 text-sm"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name={`seasons.${seasonIndex}.episodes.${episodeIndex}.audio`}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-xs">Audio URL</FormLabel>
                <FormControl>
                  <Input
                    type="url"
                    placeholder="https://example.com/episode.mp3"
                    {...field}
                    className="h-8 text-sm"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      )}
    </Card>
  );
}

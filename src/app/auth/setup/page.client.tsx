"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import type { Organization } from "better-auth/plugins";
import { CheckIcon, Loader2, XIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import {
  type FC,
  useCallback,
  useEffect,
  useRef,
  useState,
  useTransition,
} from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { authClient } from "@/auth/client";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  InputGroup,
  InputGroupAddon,
  InputGroupInput,
} from "@/components/ui/input-group";
import { Spinner } from "@/components/ui/spinner";

type ISetupClientPageProps = {
  organization: Organization;
};

export const formSchema = z.object({
  name: z
    .string({ error: "Name is required" })
    .min(3, { error: "Name must be at least 3 characters" })
    .max(64, { error: "Name must be at most 64 characters" }),
  slug: z
    .string({ error: "Slug is required" })
    .trim()
    .min(3, { error: "Slug must be at least 3 characters" })
    .max(64, { error: "Slug must be at most 64 characters" })
    .regex(
      /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
      "Use lowercase letters, numbers, and single hyphens; no leading/trailing hyphen.",
    ),
});

export const SetupClientPage: FC<ISetupClientPageProps> = ({
  organization,
}) => {
  const router = useRouter();

  const [isPending, startTransition] = useTransition();
  const [slugAvailable, setSlugAvailable] = useState(true);
  const [isChecking, setIsChecking] = useState(false);

  // Track if the user actually edited the slug manually.
  const userEditedSlugRef = useRef(false);
  // Skip the very first effect run (initial defaultValues render)
  const firstRunRef = useRef(true);
  // Prevent race conditions: only the latest request should update state
  const requestIdRef = useRef(0);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: { name: organization.name, slug: organization.slug },
    mode: "onChange",
  });

  // Lightweight format check to avoid spamming backend when the slug is obviously invalid
  const isSlugFormatValid = useCallback((s: string) => {
    if (!s) return false;
    if (s.length < 3 || s.length > 64) return false;
    return /^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(s);
  }, []);

  const watchSlug = form.watch("slug");

  useEffect(() => {
    // Don't run on first mount
    if (firstRunRef.current) {
      firstRunRef.current = false;
      return;
    }

    // Only check if user actually edited the slug manually
    if (!userEditedSlugRef.current) return;

    const s = (watchSlug ?? "").trim();

    // If format invalid, reflect unavailable immediately and don't ping backend
    if (!isSlugFormatValid(s)) {
      setIsChecking(false);
      setSlugAvailable(false);
      return;
    }

    if (s === organization.slug) {
      setIsChecking(false);
      setSlugAvailable(true);
      return;
    }

    setIsChecking(true);
    const id = ++requestIdRef.current;

    const handle = setTimeout(async () => {
      try {
        const { data } = await authClient.organization.checkSlug({ slug: s });

        if (requestIdRef.current === id) {
          setSlugAvailable(Boolean(data?.status));
        }
      } catch {
        if (requestIdRef.current === id) {
          setSlugAvailable(false);
        }
      } finally {
        if (requestIdRef.current === id) {
          setIsChecking(false);
        }
      }
    }, 500);

    return () => clearTimeout(handle);
  }, [watchSlug, isSlugFormatValid, organization]);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    startTransition(async () => {
      await authClient.organization.update({
        organizationId: organization.id,
        data: {
          name: values.name,
          slug: values.slug,
        },
        fetchOptions: {
          onError: (err) => {
            console.error(err);
            form.setError("slug", { message: err.error.message });
          },
          onSuccess: ({ data }) => {
            form.reset();
            router.push(`/${data.slug}`);
          },
        },
      });
    });
  };

  return (
    <main className="mx-auto container min-h-screen flex grow flex-col items-center justify-center self-center p-4 md:p-6">
      <Card className="w-full max-w-sm">
        <CardHeader>
          <CardTitle>Setup Organization</CardTitle>
          <CardDescription>
            Please enter your organization name and slug.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <fieldset
                className="w-full space-y-6"
                aria-busy={isPending}
                disabled={isPending}
              >
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Acme Inc." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="slug"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Slug</FormLabel>
                      <FormControl>
                        <InputGroup>
                          <InputGroupInput
                            placeholder="acme-inc"
                            {...field}
                            onChange={(e) => {
                              userEditedSlugRef.current = true; // mark that user manually edited
                              field.onChange(e);
                            }}
                          />
                          <InputGroupAddon align="inline-end">
                            {isChecking ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : slugAvailable ? (
                              <CheckIcon className="h-4 w-4 text-green-600" />
                            ) : (
                              <XIcon className="h-4 w-4 text-destructive" />
                            )}
                          </InputGroupAddon>
                        </InputGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button type="submit" className="w-full">
                  {isPending ? (
                    <>
                      <Spinner /> Saving
                    </>
                  ) : (
                    <>Save</>
                  )}
                </Button>
              </fieldset>
            </form>
          </Form>
        </CardContent>
      </Card>
    </main>
  );
};

import type { Organization } from "better-auth/plugins";
import type { NextPage } from "next";
import { headers } from "next/headers";
import { auth } from "@/auth";
import { SetupClientPage } from "./page.client";

const inferOrgName = (username: string) => {
  return `${username.split(" ")[0]}'s Team`;
};

const slugify = async (username: string) => {
  const generated = username.split(" ")[0].toLowerCase().replace(/ /g, "-");

  const { status: available } = await auth.api.checkOrganizationSlug({
    body: { slug: generated },
  });

  if (available) {
    return generated;
  }

  return `${generated}-${Math.floor(Math.random() * 1000)}`;
};

const SetupPage: NextPage = async () => {
  // const session = await auth.api.getSession({ headers: await headers() });

  // if (!session) {
  //   return <div>No session</div>;
  // }

  // const orgs = await auth.api.listOrganizations({ headers: await headers() });

  // if (orgs.length > 0) {
  //   return <div>Already setup</div>;
  // }

  // const createdOrg = await auth.api.createOrganization({
  //   headers: await headers(),
  //   body: {
  //     name: inferOrgName(session.user.name),
  //     slug: await slugify(session.user.name),
  //   },
  // });

  const org = {
    id: "xBW6VecmprkBb2WAvxqTDW1E2jZtXFnc",
    name: "bnm's Team",
    slug: "bnm",
  } as Organization;

  return <SetupClientPage organization={org} />;
};

export default SetupPage;

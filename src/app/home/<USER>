import { ChevronRight, Globe } from "lucide-react";
import type { NextPage } from "next";
import Link from "next/link";
import { Button, buttonVariants } from "@/components/ui/button";

const MarketingHomePage: NextPage = () => {
  return (
    <main className="container mx-auto">
      <section className="h-svh grid place-items-center">
        <div className="text-center space-y-2.5">
          <h1 className="text-3xl">puka.cloud</h1>
          <p className="text-lg text-muted-foreground">
            This is the marketing home page.
          </p>
          <div className="flex gap-2.5 justify-center mt-12">
            <Link
              className={buttonVariants({ size: "sm" })}
              href="/auth/sign-up"
            >
              Get Started
              <ChevronRight />
            </Link>
            <Button size="sm" variant="secondary">
              Learn More
              <Globe />
            </Button>
          </div>
        </div>
      </section>
    </main>
  );
};

export default MarketingHomePage;

"use client";

import Link from "next/link";
import { useParams } from "next/navigation";
import type { FC } from "react";

type NextLinkProps = React.ComponentProps<typeof Link>;

interface IOrgAwareLinkProps extends NextLinkProps {
  orgSlug?: string;
}

export const useOrgAwareLink = () => {
  const params = useParams<{ orgSlug: string }>();

  const generate = (href: string, orgSlug?: string) => {
    return `/${orgSlug ?? params.orgSlug}${href}`;
  };

  return { generate };
};

export const OrgAwareLink: FC<IOrgAwareLinkProps> = ({
  orgSlug,
  href,
  ...props
}) => {
  const { generate } = useOrgAwareLink();

  const finalHref = generate(String(href), orgSlug);

  return <Link href={finalHref} {...props} />;
};

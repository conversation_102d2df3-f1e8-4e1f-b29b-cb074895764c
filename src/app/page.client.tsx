"use client";

import { useRouter } from "next/navigation";
import { type FC, useEffect } from "react";
import { authClient } from "@/auth/client";

export const IndexPageClient: FC = () => {
  const router = useRouter();

  const { data: organizations, isPending: organizationsPending } =
    authClient.useListOrganizations();
  const { data: activeOrganization, isPending: activeOrganizationPending } =
    authClient.useActiveOrganization();

  useEffect(() => {
    setTimeout(() => {
      if (!organizationsPending && !activeOrganizationPending) {
        if (organizations?.length === 0) {
          router.push("/setup");
        } else if (activeOrganization) {
          router.push(`/${activeOrganization.slug}`);
        } else {
          router.push("/");
        }
      }
    }, 1000);
  }, [
    organizations,
    organizationsPending,
    activeOrganization,
    activeOrganizationPending,
    router,
  ]);

  return null;
};

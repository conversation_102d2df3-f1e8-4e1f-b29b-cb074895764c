import type { NextPage } from "next";
import { headers } from "next/headers";
import { auth } from "@/auth";
import { Spinner } from "@/components/ui/spinner";
import { TextShimmer } from "@/components/ui/text-shimmer";
import MarketingHomePage from "./home/<USER>";
import { IndexPageClient } from "./page.client";

const IndexPage: NextPage = async () => {
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session) {
    return <MarketingHomePage />;
  }

  return (
    <main className="container mx-auto">
      <IndexPageClient />
      <section className="h-svh grid place-items-center">
        <div className="space-y-2">
          <div className="flex items-center gap-4 text-sm justify-between">
            <TextShimmer className="font-mono" duration={2}>
              Redirecting
            </TextShimmer>
            <Spinner className="size-3 text-muted-foreground" />
          </div>
          <p className="text-xs font-mono text-muted-foreground">
            You are logged in as {session.user.email}
          </p>
        </div>
      </section>
    </main>
  );
};

export default IndexPage;

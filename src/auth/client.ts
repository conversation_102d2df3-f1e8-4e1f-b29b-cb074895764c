import { polarClient } from "@polar-sh/better-auth";
import {
  inferAdditionalFields,
  organizationClient,
} from "better-auth/client/plugins";
import { createAuthClient } from "better-auth/react";

export const authClient = createAuthClient({
  plugins: [
    organizationClient(),
    polarClient(),
    inferAdditionalFields({
      user: {
        setupCompleted: {
          type: "boolean",
          required: true,
        },
      },
    }),
  ],
});

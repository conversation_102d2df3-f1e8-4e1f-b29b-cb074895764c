import { betterAuth } from "better-auth";
import { drizzle<PERSON>dapter } from "better-auth/adapters/drizzle";
import { nextCookies } from "better-auth/next-js";
import {
  createAuthMiddleware,
  openAPI,
  organization,
} from "better-auth/plugins";
import { db } from "@/server/db";
import * as schema from "@/server/db/schema";
import { plugins } from "./plugins";

export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: "pg",
    schema,
  }),
  emailAndPassword: {
    enabled: true,
  },
  socialProviders: {
    github: {
      clientId: process.env.GITHUB_CLIENT_ID as string,
      clientSecret: process.env.GITHUB_CLIENT_SECRET as string,
    },
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    },
  },
  user: {
    additionalFields: {
      setupCompleted: {
        type: "boolean",
        required: false,
        defaultValue: false,
        input: true,
      },
    },
  },
  hooks: {
    after: createAuthMiddleware(async (ctx) => {
      if (ctx.path.startsWith("/sign-up")) {
        const { newSession } = ctx.context;

        if (newSession) {
          ctx.redirect(`/auth/setup?userID=${newSession.user.id}`);
        }
      }
    }),
  },
  plugins: [nextCookies(), organization(), openAPI(), ...plugins],
});

export type Session = typeof auth.$Infer.Session;

import { checkout, polar, portal, usage } from "@polar-sh/better-auth";
import { Polar } from "@polar-sh/sdk";

const polarClient = new Polar({
  accessToken: process.env.POLAR_ACCESS_TOKEN,
  server: "sandbox",
});

export const polarPlugin = polar({
  client: polarClient,
  createCustomerOnSignUp: true,
  use: [
    checkout({
      products: [
        { productId: "cf5eacd8-11e4-494a-b6eb-1459cf9c949b", slug: "free" },
        { productId: "2005a80a-1811-407b-9a56-7011c878d827", slug: "pro" },
        { productId: "67bb4981-89ab-4687-a8bb-feafc0c1ab64", slug: "business" },
      ],
      successUrl: "/success?checkout_id={CHECKOUT_ID}",
      authenticatedUsersOnly: true,
    }),
    portal(),
    usage(),
  ],
});

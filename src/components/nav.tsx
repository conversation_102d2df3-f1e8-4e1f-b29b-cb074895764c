"use client";

import { UserButton } from "@daveyplate/better-auth-ui";
import { motion } from "motion/react";
import Link from "next/link";
import { type FC, useEffect, useState } from "react";
import { OrgSwitcher } from "./org-switcher";
import { TextMorph } from "./ui/text-morph";

interface NavProps {
  showOrgSwitch?: boolean;
}

export const Nav: FC<NavProps> = ({ showOrgSwitch = true }) => {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 12) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <div className="flex h-16 items-center justify-between px-8">
      <div className="flex h-full items-center gap-x-8 pl-28">
        <Link href="/" className="fixed left-8 z-60 text-xs font-bold">
          <motion.span
            transition={{ duration: 0.2, ease: "easeOut" }}
            animate={isScrolled ? { width: 23 } : { width: 75 }}
            className="inline-block truncate bg-foreground text-background px-1 mt-1"
          >
            <span className="sr-only">puka.cloud</span>
            <TextMorph>{isScrolled ? "pc" : "puka.cloud"}</TextMorph>
          </motion.span>
        </Link>
        {showOrgSwitch && (
          <>
            <span className="h-6 w-px rotate-12 bg-border" />
            <OrgSwitcher />
          </>
        )}
      </div>
      <div className="flex h-full items-center gap-x-8">
        <UserButton size={"icon"} />
      </div>
    </div>
  );
};

"use client";

import { AuthQueryProvider } from "@daveyplate/better-auth-tanstack";
import { AuthUIProviderTanstack } from "@daveyplate/better-auth-ui/tanstack";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { ThemeProvider, useTheme } from "next-themes";
import { Toaster } from "sonner";
import { authClient } from "@/auth/client";

export function Providers({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const theme = useTheme();

  return (
    <AuthQueryProvider>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
      >
        <AuthUIProviderTanstack
          authClient={authClient}
          navigate={router.push}
          persistClient={false}
          replace={router.replace}
          onSessionChange={() => router.refresh()}
          social={{ providers: ["google", "github"] }}
          Link={Link}
        >
          {children}
          <Toaster
            theme={theme.theme as "light" | "dark" | "system"}
            toastOptions={{ className: "rounded-none" }}
          />
        </AuthUIProviderTanstack>
      </ThemeProvider>
    </AuthQueryProvider>
  );
}

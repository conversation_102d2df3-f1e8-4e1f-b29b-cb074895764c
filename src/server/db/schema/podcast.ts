import { relations, sql } from "drizzle-orm";
import { pgTable, text, timestamp, uuid } from "drizzle-orm/pg-core";
import { organization } from "./auth";

export const podcast = pgTable("podcast", {
  id: uuid("id").default(sql`gen_random_uuid()`).primaryKey(),
  name: text("name").notNull(),
  subdomain: text("subdomain").unique(),
  description: text("description"),
  image: text("image"),
  email: text("email"),
  owner: text("owner"),
  copyright: text("copyright"),
  organizationId: text("organization_id")
    .notNull()
    .references(() => organization.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .$onUpdate(() => /* @__PURE__ */ new Date())
    .notNull(),
});

export type Podcast = typeof podcast.$inferSelect;
export type Podcast_Insert = typeof podcast.$inferInsert;

export const season = pgTable("season", {
  id: uuid("id").default(sql`gen_random_uuid()`).primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  image: text("image"),
  podcastId: uuid("podcast_id")
    .notNull()
    .references(() => podcast.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .$onUpdate(() => /* @__PURE__ */ new Date())
    .notNull(),
});

export type Season = typeof season.$inferSelect;
export type Season_Insert = typeof season.$inferInsert;

export const episode = pgTable("episode", {
  id: uuid("id").default(sql`gen_random_uuid()`).primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  image: text("image"),
  audio: text("audio"),
  duration: text("duration"),
  seasonId: uuid("season_id")
    .notNull()
    .references(() => season.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .$onUpdate(() => /* @__PURE__ */ new Date())
    .notNull(),
});

export type Episode = typeof episode.$inferSelect;
export type Episode_Insert = typeof episode.$inferInsert;

export const podcastRelations = relations(podcast, ({ one, many }) => ({
  organization: one(organization, {
    fields: [podcast.organizationId],
    references: [organization.id],
  }),
  seasons: many(season),
}));

export const seasonRelations = relations(season, ({ one, many }) => ({
  podcast: one(podcast, {
    fields: [season.podcastId],
    references: [podcast.id],
  }),
  episodes: many(episode),
}));

export const episodeRelations = relations(episode, ({ one }) => ({
  season: one(season, {
    fields: [episode.seasonId],
    references: [season.id],
  }),
}));

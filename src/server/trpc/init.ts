import { initTRPC, TRPCError } from "@trpc/server";
import { auth, type Session } from "@/auth"; // BetterAuth server instance
import { db } from "@/server/db";

type ServerSession = Session;

export type Context = {
  headers: Headers;
  auth: ServerSession | null;
  user: Session["user"] | null;
};

// Build context *per request*, no cache()
export async function createTRPCContext(opts: {
  req: Request;
  resHeaders: Headers;
}): Promise<Context> {
  // BetterAuth: read once here
  const session = await auth.api.getSession({ headers: opts.req.headers });

  return {
    headers: opts.req.headers,
    auth: session ?? null,
    user: session?.user ?? null,
  };
}

const t = initTRPC.context<Context>().create();

export const createTRPCRouter = t.router;
export const publicProcedure = t.procedure;

// --- Auth middleware (user required)
const isAuthed = t.middleware(({ ctx, next }) => {
  if (!ctx.auth || !ctx.user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }
  return next({
    ctx: {
      ...ctx,
      session: ctx.auth.session,
      user: ctx.user,
    },
  });
});

export const protectedUserProcedure = t.procedure.use(isAuthed);

async function getUserOrgMembership(params: {
  userId: string;
  organizationId: string;
}) {
  const membership = await db.query.member.findFirst({
    where: (m, { eq }) =>
      eq(m.userId, params.userId) &&
      eq(m.organizationId, params.organizationId),
  });

  return {
    organizationId: params.organizationId,
    role: membership?.role ?? null,
  };
}

// Extract organizationId from input or headers; here we take it from input
// and attach `ctx.org` and `ctx.orgMember`
const withOrg = t.middleware(async ({ ctx, next }) => {
  const organizationId = ctx.auth?.session.activeOrganizationId;

  if (!organizationId) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "organizationId required",
    });
  }

  if (!ctx.user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  const membership = await getUserOrgMembership({
    userId: ctx.user.id,
    organizationId,
  });
  if (!membership) {
    throw new TRPCError({ code: "FORBIDDEN", message: "Not an org member" });
  }

  return next({
    ctx: {
      ...ctx,
      org: { id: organizationId },
      orgMember: membership,
    },
  });
});

export const protectedOrgProcedure = protectedUserProcedure.use(withOrg); // must be member
// .use(requireOrgRole(['owner', 'admin'])) // uncomment when needed

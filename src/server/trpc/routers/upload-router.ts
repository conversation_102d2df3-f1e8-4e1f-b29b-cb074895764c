import { randomUUID } from "crypto";
import fs from "fs";
import path from "path";
import z from "zod";
import { createTRPCRouter, protectedOrgProcedure } from "../init";

export const uploadRouter = createTRPCRouter({
  upload: protectedOrgProcedure
    .input(
      z.object({
        name: z.string(),
        type: z.string(),
        base64: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const uploadDir = path.join(process.cwd(), "public", "uploads");
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      const ext = path.extname(input.name) || "." + input.type.split("/")[1];
      const filename = `${randomUUID()}${ext}`;
      const filePath = path.join(uploadDir, filename);

      const base64Data = input.base64.replace(/^data:[^;]+;base64,/, "");
      const buffer = Buffer.from(base64Data, "base64");
      fs.writeFileSync(filePath, buffer);

      const fileUrl = `/uploads/${filename}`;
      return { url: fileUrl };
    }),
});
